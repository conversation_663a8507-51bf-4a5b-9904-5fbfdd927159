Stack trace:
Frame         Function      Args
0007FFFF9CE0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8BE0) msys-2.0.dll+0x1FE8E
0007FFFF9CE0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FB8) msys-2.0.dll+0x67F9
0007FFFF9CE0  000210046832 (000210286019, 0007FFFF9B98, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CE0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9CE0  000210068E24 (0007FFFF9CF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9FC0  00021006A225 (0007FFFF9CF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB96100000 ntdll.dll
7FFB94F00000 KERNEL32.DLL
7FFB93310000 KERNELBASE.dll
7FFB8FEA0000 apphelp.dll
7FFB95310000 USER32.dll
7FFB939C0000 win32u.dll
7FFB95CB0000 GDI32.dll
7FFB93700000 gdi32full.dll
7FFB93DF0000 msvcp_win.dll
7FFB939F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB94D50000 advapi32.dll
7FFB96010000 msvcrt.dll
7FFB94BB0000 sechost.dll
7FFB942C0000 RPCRT4.dll
7FFB92A00000 CRYPTBASE.DLL
7FFB93BD0000 bcryptPrimitives.dll
7FFB95ED0000 IMM32.DLL
